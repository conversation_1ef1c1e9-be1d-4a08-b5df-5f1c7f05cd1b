/**
 * Global Styles
 *
 * This file contains global styles for the ConnectChain admin panel.
 */

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --color-primary: #F28B22;
  --color-primary-dark: #D17311;
  --color-primary-light: #F9B16F;
  --color-secondary: #D1D1D1;
  --color-secondary-dark: #B5B5B5;
  --color-secondary-light: #EBEBEB;
  --color-danger: #EF4444;
  --color-warning: #F59E0B;
  --color-success: #10B981;
  --color-info: #3B82F6;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #1F2937;
  background-color: #F9FAFB;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #F3F4F6;
}

::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}

/* Custom focus styles - only show focus rings for keyboard navigation */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* Remove focus outline on mouse click for interactive elements */
button:focus:not(:focus-visible),
a:focus:not(:focus-visible),
[role="button"]:focus:not(:focus-visible),
[role="tab"]:focus:not(:focus-visible),
[tabindex]:focus:not(:focus-visible) {
  outline: none;
}

/* Custom utility classes */
.text-primary {
  color: var(--color-primary);
}

.bg-primary {
  background-color: var(--color-primary);
}

.border-primary {
  border-color: var(--color-primary);
}

.hover\:text-primary:hover {
  color: var(--color-primary);
}

.hover\:bg-primary:hover {
  background-color: var(--color-primary);
}

.hover\:border-primary:hover {
  border-color: var(--color-primary);
}

/* Transitions */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.animate-slideInFromRight {
  animation: slideInFromRight 0.3s ease-in-out;
}

/* Form elements */
input, select, textarea {
  @apply rounded-md border-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50;
}

/* Remove focus ring on mouse click for form elements, keep for keyboard navigation */
input:focus:not(:focus-visible),
select:focus:not(:focus-visible),
textarea:focus:not(:focus-visible) {
  @apply ring-0;
}

/* Buttons */
button {
  @apply focus:outline-none;
}

button:focus-visible {
  @apply ring-2 ring-offset-2 ring-primary;
}

/* SVG and Icon fixes */
svg {
  /* Ensure SVG icons render properly */
  fill: currentColor;
  stroke: currentColor;
}

/* Heroicons specific fixes */
svg[data-slot="icon"] {
  /* Preserve Heroicons default styling */
  fill: none;
  stroke: currentColor;
  stroke-width: 1.5;
}

/* Prevent text color from affecting icon structure */
.icon-container svg {
  /* Reset any inherited text styles that might affect SVG rendering */
  font-family: inherit;
  font-size: inherit;
}

/* Tables */
table {
  @apply min-w-full divide-y divide-gray-200;
}

thead {
  @apply bg-gray-50;
}

th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

tbody {
  @apply bg-white divide-y divide-gray-200;
}

td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-500;
}
