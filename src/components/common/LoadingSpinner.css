/* Loading Spinner Animations */

/* Smooth spinner rotation */
@keyframes spin-smooth {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Bouncing dots with staggered timing */
@keyframes bounce-dot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Smooth pulse animation */
@keyframes pulse-smooth {
  0%, 100% {
    opacity: 0.4;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Ripple effect */
@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Apply custom animations */
.spinner-smooth {
  animation: spin-smooth 1s linear infinite;
}

.dots-bounce .dot {
  animation: bounce-dot 1.4s ease-in-out infinite both;
}

.dots-bounce .dot:nth-child(1) { animation-delay: -0.32s; }
.dots-bounce .dot:nth-child(2) { animation-delay: -0.16s; }
.dots-bounce .dot:nth-child(3) { animation-delay: 0s; }

.pulse-smooth {
  animation: pulse-smooth 2s ease-in-out infinite;
}

.ripple-effect {
  position: relative;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  border: 2px solid currentColor;
  animation: ripple 1.5s ease-out infinite;
}
