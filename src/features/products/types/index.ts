/**
 * Products Types
 *
 * This file defines the TypeScript interfaces for the products feature.
 */

// Backend API response wrapper
export interface ApiResponseWrapper<T> {
  success: boolean;
  message: string;
  data: T;
}

// Backend pagination response
export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  pages: number;
}

// Product attribute types
export interface ProductAttribute {
  id: string;
  name: string;
  value: string;
  type: 'text' | 'number' | 'boolean' | 'select';
  unit?: string | undefined;
}

// Product variant types
export interface ProductVariant {
  id: string;
  name: string;
  sku: string;
  price: number;
  stock: number;
  attributes: Record<string, string>;
  image?: string | undefined;
}

// Backend product interface matching API specification
export interface BackendProduct {
  id: string;
  Name: string;                    // Backend uses capital N
  SKU: string;                     // Backend uses capital SKU
  Category: string;                // Backend uses capital C
  Price: number;                   // Backend uses capital P
  Stock: number;                   // Backend uses capital S
  MinimumStock: number;            // Backend uses capital M and S
  Status: 'active' | 'inactive' | 'out_of_stock';  // Backend uses capital S
  Description?: string | undefined; // Backend uses capital D
  Image?: string | undefined;      // Backend uses capital I - single image URL
  Images?: string[] | undefined;   // Backend uses capital I - multiple image URLs
  SupplierId: string;              // Backend uses capital S and I
  Attributes?: BackendProductAttribute[] | undefined;  // Backend uses capital A
  Variants?: BackendProductVariant[] | undefined;      // Backend uses capital V
  CreatedAt: string;               // Backend uses capital C and A
  UpdatedAt: string;               // Backend uses capital U and A
}

// Backend attribute interface
export interface BackendProductAttribute {
  Id: string;                      // Backend uses capital I
  Name: string;                    // Backend uses capital N
  Value: string;                   // Backend uses capital V
  Type: 'text' | 'number' | 'boolean' | 'select';  // Backend uses capital T
  Unit?: string | undefined;       // Backend uses capital U
}

// Backend variant interface
export interface BackendProductVariant {
  Id: string;                      // Backend uses capital I
  Name: string;                    // Backend uses capital N
  SKU: string;                     // Backend uses capital SKU
  Price: number;                   // Backend uses capital P
  Stock: number;                   // Backend uses capital S
  Attributes: Record<string, string>;  // Backend uses capital A
  Image?: string | undefined;      // Backend uses capital I
}

// Frontend product interface (for UI compatibility)
export interface Product {
  id: string;
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  minimumStock: number;
  status: 'active' | 'inactive' | 'out_of_stock';
  description?: string | undefined;
  image?: string | undefined;      // Primary image
  images?: string[] | undefined;   // All images
  supplierId: string;
  attributes?: ProductAttribute[] | undefined;
  variants?: ProductVariant[] | undefined;
  createdAt: string;
  updatedAt: string;
}

// Form data interface for creating/updating products
export interface ProductFormData {
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  minimumStock: number;
  status: 'active' | 'inactive' | 'out_of_stock';
  description?: string | undefined;
  supplierId: string;
  attributes?: ProductAttribute[] | undefined;
  variants?: ProductVariant[] | undefined;
  // Images handled separately through upload endpoint
}

// Frontend form data that allows File objects for images
export interface ProductFormDataWithImages extends Omit<ProductFormData, 'images'> {
  images?: (File | string)[];
}

// Backend API query parameters for products
export interface ProductQueryParams {
  page?: number;                   // Page number (default: 1)
  limit?: number;                  // Items per page (default: 20, max: 100)
  search?: string;                 // Search in product name and SKU
  category?: string;               // Filter by category
  status?: 'active' | 'inactive' | 'out_of_stock';  // Filter by status
  supplierId?: string;             // Filter by supplier
  minPrice?: number;               // Minimum price filter
  maxPrice?: number;               // Maximum price filter
  inStock?: boolean;               // Filter products in stock
  sort?: 'Name' | 'SKU' | 'Price' | 'Stock' | 'CreatedAt' | 'UpdatedAt';  // Sort field
  order?: 'asc' | 'desc';          // Sort order
}

// Backend products list response
export interface ProductsListResponse extends ApiResponseWrapper<BackendProduct[]> {
  pagination?: PaginationInfo;
}

// Image upload response
export interface ImageUploadResponse {
  success: boolean;
  message: string;
  imageUrls: string[];
}

// Product status update
export interface ProductStatusUpdate {
  status: 'active' | 'inactive' | 'out_of_stock';
}

// Product analytics data
export interface ProductAnalyticsData {
  totalProducts: number;
  activeProducts: number;
  inactiveProducts: number;
  outOfStockProducts: number;
  lowStockProducts: number;
  averagePrice: number;
  totalValue: number;
  topCategories: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  recentProducts: Product[];
}

// Note: All types are already exported above with their declarations
