/**
 * Product Data Transformers
 *
 * This file provides utilities for transforming data between backend and frontend formats.
 */

import type {
  Product,
  BackendProduct,
  ProductFormData,
  ProductQueryParams,
  ProductAttribute,
  ProductVariant,
  BackendProductAttribute,
  BackendProductVariant,
  ApiResponseWrapper,
  ProductsListResponse
} from '../types';

/**
 * Transform backend product attribute to frontend format
 */
export const transformAttributeFromBackend = (backendAttr: BackendProductAttribute): ProductAttribute => ({
  id: backendAttr.Id,
  name: backendAttr.Name,
  value: backendAttr.Value,
  type: backendAttr.Type,
  unit: backendAttr.Unit
});

/**
 * Transform frontend product attribute to backend format
 */
export const transformAttributeToBackend = (frontendAttr: ProductAttribute): BackendProductAttribute => ({
  Id: frontendAttr.id,
  Name: frontendAttr.name,
  Value: frontendAttr.value,
  Type: frontendAttr.type,
  Unit: frontendAttr.unit
});

/**
 * Transform backend product variant to frontend format
 */
export const transformVariantFromBackend = (backendVariant: BackendProductVariant): ProductVariant => ({
  id: backendVariant.Id,
  name: backendVariant.Name,
  sku: backendVariant.SKU,
  price: backendVariant.Price,
  stock: backendVariant.Stock,
  attributes: backendVariant.Attributes,
  image: backendVariant.Image
});

/**
 * Transform frontend product variant to backend format
 */
export const transformVariantToBackend = (frontendVariant: ProductVariant): BackendProductVariant => ({
  Id: frontendVariant.id,
  Name: frontendVariant.name,
  SKU: frontendVariant.sku,
  Price: frontendVariant.price,
  Stock: frontendVariant.stock,
  Attributes: frontendVariant.attributes,
  Image: frontendVariant.image
});

/**
 * Transform backend product to frontend format
 */
export const transformProductFromBackend = (backendProduct: BackendProduct): Product => ({
  id: backendProduct.id,
  name: backendProduct.Name,
  sku: backendProduct.SKU,
  category: backendProduct.Category,
  price: backendProduct.Price,
  stock: backendProduct.Stock,
  minimumStock: backendProduct.MinimumStock,
  status: backendProduct.Status,
  description: backendProduct.Description,
  image: backendProduct.Image,
  images: backendProduct.Images,
  supplierId: backendProduct.SupplierId,
  attributes: backendProduct.Attributes?.map(transformAttributeFromBackend),
  variants: backendProduct.Variants?.map(transformVariantFromBackend),
  createdAt: backendProduct.CreatedAt,
  updatedAt: backendProduct.UpdatedAt
});

/**
 * Transform frontend product form data to backend format
 */
export const transformProductFormToBackend = (formData: ProductFormData): Partial<BackendProduct> => ({
  Name: formData.name,
  SKU: formData.sku,
  Category: formData.category,
  Price: formData.price,
  Stock: formData.stock,
  MinimumStock: formData.minimumStock,
  Status: formData.status,
  Description: formData.description,
  SupplierId: formData.supplierId,
  Attributes: formData.attributes?.map(transformAttributeToBackend),
  Variants: formData.variants?.map(transformVariantToBackend)
});

/**
 * Transform frontend query parameters to backend format
 */
export const transformQueryParamsToBackend = (params: ProductQueryParams): Record<string, any> => {
  const backendParams: Record<string, any> = {};

  if (params.page !== undefined) backendParams.page = params.page;
  if (params.limit !== undefined) backendParams.limit = params.limit;
  if (params.search !== undefined) backendParams.search = params.search;
  if (params.category !== undefined) backendParams.category = params.category;
  if (params.status !== undefined) backendParams.status = params.status;
  if (params.supplierId !== undefined) backendParams.supplierId = params.supplierId;
  if (params.minPrice !== undefined) backendParams.minPrice = params.minPrice;
  if (params.maxPrice !== undefined) backendParams.maxPrice = params.maxPrice;
  if (params.inStock !== undefined) backendParams.inStock = params.inStock;
  if (params.sort !== undefined) backendParams.sort = params.sort;
  if (params.order !== undefined) backendParams.order = params.order;

  return backendParams;
};

/**
 * Transform backend products list response to frontend format
 */
export const transformProductsListResponse = (response: any): ApiResponseWrapper<Product[]> => {
  // Handle direct array response (fallback)
  if (Array.isArray(response)) {
    return {
      success: true,
      message: 'Products retrieved successfully',
      data: response.map(transformProductFromBackend)
    };
  }

  // Handle wrapped response
  if (response.success !== undefined && response.data) {
    return {
      success: response.success,
      message: response.message || 'Products retrieved successfully',
      data: Array.isArray(response.data) 
        ? response.data.map(transformProductFromBackend)
        : []
    };
  }

  // Handle unexpected format
  throw new Error('Invalid response format from products API');
};

/**
 * Transform backend product response to frontend format
 */
export const transformProductResponse = (response: any): Product => {
  // Handle direct product response
  if (response.id || response.Id) {
    return transformProductFromBackend(response);
  }

  // Handle wrapped response
  if (response.success && response.data) {
    return transformProductFromBackend(response.data);
  }

  throw new Error('Invalid product response format');
};

/**
 * Validate backend response structure
 */
export const validateBackendResponse = <T>(response: any): ApiResponseWrapper<T> => {
  if (!response) {
    throw new Error('No response received from server');
  }

  if (response.success === false) {
    throw new Error(response.message || 'Request failed');
  }

  if (response.success === undefined) {
    // Handle direct data response (legacy)
    return {
      success: true,
      message: 'Request successful',
      data: response
    };
  }

  return response;
};

/**
 * Extract error message from backend response
 */
export const extractErrorMessage = (errorResponse: any): string => {
  if (typeof errorResponse === 'string') {
    return errorResponse;
  }

  if (errorResponse?.message) {
    return errorResponse.message;
  }

  if (errorResponse?.error) {
    return errorResponse.error;
  }

  if (errorResponse?.errors && Array.isArray(errorResponse.errors)) {
    return errorResponse.errors.join(', ');
  }

  return 'An unexpected error occurred';
};

/**
 * Validate product form data
 */
export const validateProductFormData = (formData: Partial<ProductFormData>): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!formData.name?.trim()) {
    errors.name = 'Product name is required';
  }

  if (!formData.sku?.trim()) {
    errors.sku = 'SKU is required';
  }

  if (!formData.category?.trim()) {
    errors.category = 'Category is required';
  }

  if (formData.price === undefined || formData.price < 0) {
    errors.price = 'Valid price is required';
  }

  if (formData.stock === undefined || formData.stock < 0) {
    errors.stock = 'Valid stock quantity is required';
  }

  if (formData.minimumStock === undefined || formData.minimumStock < 0) {
    errors.minimumStock = 'Valid minimum stock is required';
  }

  if (formData.stock !== undefined && formData.minimumStock !== undefined && formData.stock < formData.minimumStock) {
    errors.stock = 'Stock cannot be less than minimum stock';
  }

  if (!formData.supplierId?.trim()) {
    errors.supplierId = 'Supplier is required';
  }

  return errors;
};

// Export all transformers
export default {
  transformProductFromBackend,
  transformProductFormToBackend,
  transformQueryParamsToBackend,
  transformProductsListResponse,
  transformProductResponse,
  validateBackendResponse,
  extractErrorMessage,
  validateProductFormData,
  transformAttributeFromBackend,
  transformAttributeToBackend,
  transformVariantFromBackend,
  transformVariantToBackend
};
